
.PHONY: clean build test test-iteration1 test-iteration2 test-unittest test_iteration1 test_iteration2 test_unittest release tag all bump-patch bump-minor bump-major commit-and-push publish-patch publish-minor publish-major manual-patch-release manual-minor-release manual-major-release docker-build docker-run docker-stop docker-test docker-publish docker-export docker-clean

# File Paths
version_file = VERSION

# Ensure VERSION file exists with default value
$(shell if [ ! -f $(version_file) ]; then echo "1.0.0" > $(version_file); fi)

current_version = $(shell cat $(version_file) | tr -d ' ')
dev_version = $(current_version)-SNAPSHOT

# JAR Path Configuration
LIBS_DIR ?= ./libs
REFERENCE_SERVER_VERSION ?= 0.2.3
REFERENCE_SERVER_JAR ?= $(LIBS_DIR)/reference-server-$(REFERENCE_SERVER_VERSION).jar
CUSTOM_SERVER_JAR ?= $(LIBS_DIR)/my-server-$(current_version).jar

# Port Configuration
REFERENCE_SERVER_PORT ?= 5000
CUSTOM_SERVER_PORT ?= 5001
DOCKER_TEST_PORT ?= 5050
DOCKER_RUN_PORT ?= 5000

# Docker registry configuration (use GitLab CI variables if available, fallback to defaults)
DOCKER_REGISTRY ?= registry.gitlab.wethinkco.de
********************* ?= $(DOCKER_REGISTRY)/jaarnoljhb024/brownfields_robot_worlds_3

define bump_version
$(eval MAJOR = $(shell echo $(current_version) | cut -d '.' -f 1))
$(eval MINOR = $(shell echo $(current_version) | cut -d '.' -f 2))
$(eval PATCH = $(shell echo $(current_version) | cut -d '.' -f 3))
endef

# Clean
clean:
	@echo "Cleaning project..."
	@mvn clean

# Build
build:
	@echo "Building project..."
	@mvn clean install -DskipTests

# Test
test:
	@echo "Running all tests (iteration1, iteration2, unittest)..."
	@$(MAKE) test-iteration1
	@$(MAKE) test-iteration2
	@$(MAKE) test-unittest

test-iteration1:
	@echo "Cleaning up any existing servers..."
	@if [ -f reference-server.pid ]; then kill `cat reference-server.pid` 2>/dev/null || true; rm -f reference-server.pid; fi
	@sleep 2
	@echo "Starting reference server with 1x1 world on port $(REFERENCE_SERVER_PORT)..."
	@java -jar $(REFERENCE_SERVER_JAR) -p $(REFERENCE_SERVER_PORT) -s 1 > reference-server.log 2>&1 &
	@echo $$! > reference-server.pid
	@sleep 5
	@echo "Running acceptance tests..."
	@mvn -Pacceptance-tests -Dtest=acceptance_test.LaunchAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.LookAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.robotState.RobotStateAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.robotState.RobotStateNonExistentAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.WorldTests.WorldRobotDeathPitAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.WorldTests.WorldInitializationAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.WorldTests.WorldPositionValidationAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.WorldTests.WorldRobotAdditionAcceptanceTest test
	@echo "All tests completed. Now killing reference server..."
	@if [ -f reference-server.pid ]; then kill `cat reference-server.pid` 2>/dev/null || true; rm -f reference-server.pid; fi

test_iteration1: test-iteration1

test-iteration2:
	@echo "Cleaning up any existing servers..."
	@if [ -f reference-server.pid ]; then kill `cat reference-server.pid` 2>/dev/null || true; rm -f reference-server.pid; fi
	@sleep 2
	@echo "Starting reference server with 2x2 world on port $(REFERENCE_SERVER_PORT)..."
	@java -jar $(REFERENCE_SERVER_JAR) -p $(REFERENCE_SERVER_PORT) -s 2 -o 1,1 > reference-server.log 2>&1 &
	@echo $$! > reference-server.pid
	@sleep 5
	@echo "Running acceptance tests..."
	@mvn -Pacceptance-tests -Dtest=acceptance_test.LaunchRobotAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.Moveforward.MoveForwardAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.Moveforward.MoveForwardEdgeAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.look.LookAcceptanceTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.look.LookDetectObstacleTest test
	@mvn -Pacceptance-tests -Dtest=acceptance_test.look.LookDetectRobotsAndObstacleTest test
	@echo "Killing reference server..."
	@if [ -f reference-server.pid ]; then kill `cat reference-server.pid` 2>/dev/null || true; rm -f reference-server.pid; fi

test_iteration2: test-iteration2

test-unittest:
	@echo "Cleaning up any existing servers..."
	@if [ -f server.pid ]; then kill `cat server.pid` 2>/dev/null || true; rm -f server.pid; fi
	@sleep 3
	@echo "Starting My codebase server on port $(CUSTOM_SERVER_PORT)..."
	@mvn exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.MultiServers -Dexec.args="-p $(CUSTOM_SERVER_PORT) -s 2" > server.log 2>&1 &
	@echo $$! > server.pid
	@sleep 5
	@echo "Running unit tests (excluding acceptance tests)..."
	@echo "Starting tests with 60 second timeout protection..."
	@(sleep 60 && echo "Timeout reached, killing processes..." && if [ -f server.pid ]; then kill `cat server.pid` 2>/dev/null || true; rm -f server.pid; fi) &
	@mvn test -Djava.awt.headless=true
	@echo "Killing My codebase server..."
	@echo "timeout thread interrupted, normal shutdown"
	@sleep 2
	@if [ -f server.pid ]; then kill `cat server.pid` 2>/dev/null || true; rm -f server.pid; fi

test_unittest: test-unittest

# Release
release:
	@echo "Switch to release version..."
	@sed -i 's|<version>$(dev_version)</version>|<version>$(current_version)</version>|g' pom.xml
	@mvn clean package -DskipTests
	@echo "Switch back to new version..."
	@sed -i 's|<version>$(current_version)</version>|<version>$(dev_version)</version>|g' pom.xml
	@cp target/robot-world-$(current_version)-jar-with-dependencies.jar libs/my-server-$(current_version).jar
	@git add libs/my-server-$(current_version).jar

# Tag
tag:
	@echo "Tagging release..."
	@git tag -a v$(current_version) -m "Release v$(current_version)"
	@if [ -n "$$CI_JOB_TOKEN" ]; then \
		echo "Running in GitLab CI, using CI token for authentication..."; \
		git remote set-url origin https://gitlab-ci-token:$$<EMAIL>/jaarnoljhb024/brownfields_robot_worlds_3.git; \
	fi
	@git push origin v$(current_version)

# Bump Patch
bump-patch:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		PATCH=$(PATCH); \
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s|<version>$(current_version)-SNAPSHOT</version>|<version>$$NEW_VERSION-SNAPSHOT</version>|g" pom.xml \
	'

# Bump Minor
bump-minor:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s|<version>$(current_version)-SNAPSHOT</version>|<version>$$NEW_VERSION-SNAPSHOT</version>|g" pom.xml \
	'

# Bump Major
bump-major:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s|<version>$(current_version)-SNAPSHOT</version>|<version>$$NEW_VERSION-SNAPSHOT</version>|g" pom.xml \
	'

# Commit and Push
commit-and-push:
	@echo "Committing changes..."
	@git add .
	@git commit -m "Bump version to $(current_version)"
	@echo "Pushing changes..."
	@if [ -n "$$CI_JOB_TOKEN" ]; then \
		echo "Running in GitLab CI, using CI token for authentication..."; \
		git remote set-url origin https://gitlab-ci-token:$$<EMAIL>/jaarnoljhb024/brownfields_robot_worlds_3.git; \
	fi
	@git push

all:
	@$(MAKE) clean
	@$(MAKE) test_iteration1
	@$(MAKE) test_iteration2
	@$(MAKE) test_unittest
	@$(MAKE) build
	@$(MAKE) release

publish-patch:
	@$(MAKE) bump-patch
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

publish-minor:
	@$(MAKE) bump-minor
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

publish-major:
	@$(MAKE) bump-major
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

# Docker targets
docker-build:
	@echo "Building Docker image..."
	@echo "Using existing JAR from target/ directory..."
	@docker build -t robot-worlds-server:$(current_version) .
	@docker tag robot-worlds-server:$(current_version) robot-worlds-server:latest
	@echo "Docker image built: robot-worlds-server:$(current_version)"

docker-run:
	@echo "Running Docker container on port $(DOCKER_RUN_PORT)..."
	@docker run -d --name robot-worlds-server -p $(DOCKER_RUN_PORT):$(DOCKER_RUN_PORT) robot-worlds-server:latest
	@echo "Container started. Server available at http://localhost:$(DOCKER_RUN_PORT)"
	@echo "To stop: make docker-stop"

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop robot-worlds-server || true
	@docker rm robot-worlds-server || true

docker-test:
	@echo "Testing Docker container with acceptance tests (using existing image)..."
	@echo "Starting Docker container on port $(DOCKER_TEST_PORT)..."
	@docker run -d --name robot-worlds-test -p $(DOCKER_TEST_PORT):$(DOCKER_TEST_PORT) robot-worlds-server:$(current_version)
	@echo "Waiting for container to be ready..."
	@sleep 10
	@echo "Verifying container is running..."
	@if ! docker ps | grep -q robot-worlds-test; then \
		echo "Container failed to start properly"; \
		docker logs robot-worlds-test; \
		docker stop robot-worlds-test 2>/dev/null; \
		docker rm robot-worlds-test 2>/dev/null; \
		exit 1; \
	fi
	@echo "Container is running. Checking server logs..."
	@docker logs robot-worlds-test
	@echo "Container is ready. Running acceptance tests against Docker container on port 5050..."
	@echo "Verifying acceptance-tests profile configuration..."
	@mvn -Pacceptance-tests -Dserver.port=5050 help:active-profiles
	@echo "Compiling test classes..."
	@mvn -Pacceptance-tests -Dserver.port=5050 test-compile || (echo "Test compilation failed"; docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@echo "Running iteration 1 tests..."
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.LaunchAcceptanceTest test || (echo "Test failed, checking container logs:"; docker logs robot-worlds-test; docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.LookAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.robotState.RobotStateAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.robotState.RobotStateNonExistentAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.WorldTests.WorldRobotDeathPitAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.WorldTests.WorldInitializationAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.WorldTests.WorldPositionValidationAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.WorldTests.WorldRobotAdditionAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@echo "Running iteration 2 tests..."
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.LaunchRobotAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.Moveforward.MoveForwardAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.Moveforward.MoveForwardEdgeAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.look.LookAcceptanceTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.look.LookDetectObstacleTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@mvn -Pacceptance-tests -Dserver.port=5050 -Dtest=acceptance_test.look.LookDetectRobotsAndObstacleTest test || (docker stop robot-worlds-test 2>/dev/null; docker rm robot-worlds-test 2>/dev/null; exit 1)
	@echo "Docker container acceptance tests completed successfully"
	@echo "Stopping test container..."
	@docker stop robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-test 2>/dev/null || true


docker-publish:
	@echo "Publishing Docker image to GitLab Container Registry..."
	@echo "Registry: $(*********************)"
	@echo "Building Docker image..."
	@$(MAKE) docker-build
	@docker tag robot-worlds-server:$(current_version) $(*********************):$(current_version)
	@docker tag robot-worlds-server:$(current_version) $(*********************):latest
	@docker push $(*********************):$(current_version)
	@docker push $(*********************):latest
	@echo "Docker image published to GitLab Container Registry"

docker-export:
	@echo "Exporting Docker image to tar file..."
	@$(MAKE) docker-build
	@docker save robot-worlds-server:$(current_version) -o robot-worlds-server-$(current_version).tar
	@echo "Docker image exported: robot-worlds-server-$(current_version).tar"

docker-clean:
	@echo "Cleaning up Docker images and containers..."
	@docker stop robot-worlds-server robot-worlds-test robot-worlds-test-iter1 robot-worlds-test-iter2 2>/dev/null || true
	@docker rm robot-worlds-server robot-worlds-test robot-worlds-test-iter1 robot-worlds-test-iter2 2>/dev/null || true
	@docker rmi robot-worlds-server:$(current_version) robot-worlds-server:latest 2>/dev/null || true
	@rm -f robot-worlds-server-*.tar 2>/dev/null || true
	@echo "Docker cleanup completed"