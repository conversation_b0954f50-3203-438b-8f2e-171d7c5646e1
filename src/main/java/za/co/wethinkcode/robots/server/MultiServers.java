package za.co.wethinkcode.robots.server;

import picocli.CommandLine;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.world.World;
import java.io.*;
import java.sql.SQLException;
import java.util.Scanner;
import java.util.concurrent.ConcurrentHashMap;


@CommandLine.Command(name="Multi-server",
        mixinStandardHelpOptions = true,
        version="1.0",
        description = "command line server inputs")

/**
 * MultiServers class to handle multiple client connections and server commands.
 */
public class MultiServers implements Runnable {
    public static final ConcurrentHashMap<String, Server> clientHandlerMap = new ConcurrentHashMap<>();

    private static MultiServerEngine server;
    private static boolean isUnitTestMode = false;
    private static MultiServers servers;
    public static MultiServerEngine getServer() {
        return server;
    }
    private static World worldInstance;



    @CommandLine.Option(names = {"-p", "--port"}, description = "port number to connect to", required = false, defaultValue = "5000")
    private int port;

    @CommandLine.Option(names = {"-hei", "--height"}, description = "world size", required = false, defaultValue = "-1")
    private int height;

    @CommandLine.Option(names = {"-w", "--width"}, description = "world size", required = false, defaultValue = "-1")
    private int width;


    @CommandLine.Option(names = {"-o", "--obstacle"}, description = "position of obstacle in the world or none", defaultValue = "none")
    private String obstacle;

    public int getPort() {
        return port;
    }

    public int getHeight() {
        return height;
    }

    public int getWidth() {
        return width;
    }

    public String getObstacle() {
        return obstacle;
    }



    @Override
    public void run() {
        ObstacleType type = ObstacleType.MOUNTAIN;

        Config.loadConfig("config.properties");
        ConfigOverrideHelper.OverRideConfigProperties(this);

        Maze maze = new Maze(" ");

        worldInstance = WorldInstanceHelper. createInstance(this, maze, type);
        server = new MultiServerEngine(worldInstance);

        if (!ServerStartupHelper.startServer(server, port)) {
            return;
        }

        if (TestEnvironmentHelper.shouldExitAfterAcceptanceTest()) {
            ServerShutdownHelper.shutdownServer(server);
            return;
        }

        if (System.console() == null) {
            UnitTestModeHandler.handleUnitTestMode(server, () -> isUnitTestMode = true);
            return;
        }

        Scanner scanner = new Scanner(System.in);
        ServerShutdownHelper.addShutdownHook(server, scanner);

        try {
            ServerCommandHandler.handleCommands(scanner, worldInstance, server,this);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        ServerShutdownHelper.shutdownServerAndCloseScanner(server, scanner);
    }


    static {
        try {
            if (!"true".equals(System.getProperty("flow.disable"))) {
                new Recorder().logRun();
            }
        } catch (Exception e) {
            System.err.println("Flow library initialization skipped: " + e.getMessage());
        }
    }

    public static void printServerPrompt() {
        ServerPromptHelper.printPrompt(isUnitTestMode);
    }


    public static void main(String[] args) {
        int exitCode = (new CommandLine(new MultiServers())).execute(args);
        System.exit(exitCode);
    }

    public static void setWorldInstance(World worldInstance) {
        MultiServers.worldInstance = worldInstance;
    }

    public static World getWorldInstance(){
        return worldInstance;
    }
}

